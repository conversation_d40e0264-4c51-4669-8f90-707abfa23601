// Gantt Chart Utility Functions
import { format, addDays, addWeeks, addMonths, addYears, startOfWeek, startOfMonth, startOfYear, endOfWeek, endOfMonth, endOfYear, differenceInDays, differenceInWeeks, differenceInMonths, differenceInYears } from 'date-fns'

export type TimeScale = 'day' | 'week' | 'month' | 'year'

export interface TimeUnit {
  date: Date
  label: string
  subLabel?: string
  width: number
}

export interface GanttTask {
  id: string
  name: string
  startDate: Date
  endDate: Date
  progress: number
  status: 'not-started' | 'in-progress' | 'completed' | 'on-hold'
  responsible: string
  textileProcess?: string
  parent?: string
  type: 'project' | 'task'
  odooId?: number
  projectId?: number
  stageId?: number
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  dependencies?: string[]
}

export interface TimeScaleConfig {
  scale: TimeScale
  label: string
  pixelsPerUnit: number
  headerFormat: string
  subHeaderFormat?: string
  unitDuration: number // in days
  addFunction: (date: Date, amount: number) => Date
  startFunction: (date: Date) => Date
  endFunction: (date: Date) => Date
  differenceFunction: (dateLeft: Date, dateRight: Date) => number
}

// Time scale configurations
export const timeScaleConfigs: Record<TimeScale, TimeScaleConfig> = {
  day: {
    scale: 'day',
    label: 'Day View',
    pixelsPerUnit: 40,
    headerFormat: 'MMM dd',
    subHeaderFormat: 'EEE',
    unitDuration: 1,
    addFunction: addDays,
    startFunction: (date: Date) => new Date(date.getFullYear(), date.getMonth(), date.getDate()),
    endFunction: (date: Date) => new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59),
    differenceFunction: differenceInDays
  },
  week: {
    scale: 'week',
    label: 'Week View',
    pixelsPerUnit: 120,
    headerFormat: 'MMM dd',
    subHeaderFormat: "'Week' w",
    unitDuration: 7,
    addFunction: addWeeks,
    startFunction: startOfWeek,
    endFunction: endOfWeek,
    differenceFunction: differenceInWeeks
  },
  month: {
    scale: 'month',
    label: 'Month View',
    pixelsPerUnit: 80,
    headerFormat: 'MMM yyyy',
    unitDuration: 30,
    addFunction: addMonths,
    startFunction: startOfMonth,
    endFunction: endOfMonth,
    differenceFunction: differenceInMonths
  },
  year: {
    scale: 'year',
    label: 'Year View',
    pixelsPerUnit: 100,
    headerFormat: 'yyyy',
    unitDuration: 365,
    addFunction: addYears,
    startFunction: startOfYear,
    endFunction: endOfYear,
    differenceFunction: differenceInYears
  }
}

// Generate time units for timeline
export const generateTimeUnits = (
  startDate: Date,
  endDate: Date,
  timeScale: TimeScale
): TimeUnit[] => {
  const config = timeScaleConfigs[timeScale]
  const units: TimeUnit[] = []
  
  let currentDate = config.startFunction(startDate)
  const finalDate = config.endFunction(endDate)
  
  while (currentDate <= finalDate) {
    const unitEnd = config.endFunction(currentDate)
    
    let label: string
    let subLabel: string | undefined
    
    switch (timeScale) {
      case 'day':
        label = format(currentDate, 'MMM dd')
        subLabel = format(currentDate, 'EEE')
        break
      case 'week':
        const weekEnd = endOfWeek(currentDate)
        label = `${format(currentDate, 'MMM dd')} - ${format(weekEnd, 'MMM dd')}`
        subLabel = `Week ${format(currentDate, 'w')}`
        break
      case 'month':
        label = format(currentDate, 'MMM yyyy')
        break
      case 'year':
        label = format(currentDate, 'yyyy')
        break
    }
    
    units.push({
      date: new Date(currentDate),
      label,
      subLabel,
      width: config.pixelsPerUnit
    })
    
    currentDate = config.addFunction(currentDate, 1)
  }
  
  return units
}

// Calculate task position and width
export const calculateTaskPosition = (
  task: GanttTask,
  timelineStart: Date,
  timeScale: TimeScale
): { left: number; width: number } => {
  const config = timeScaleConfigs[timeScale]

  if (!config) {
    console.error('Invalid time scale:', timeScale)
    return { left: 0, width: 100 }
  }

  try {
    // Calculate the offset from timeline start
    const startOffset = config.differenceFunction(task.startDate, timelineStart)
    const duration = config.differenceFunction(task.endDate, task.startDate) + 1 // +1 to include end date

    // Ensure minimum width for visibility
    const minWidth = timeScale === 'day' ? 20 : timeScale === 'week' ? 30 : timeScale === 'month' ? 40 : 50

    return {
      left: Math.max(0, startOffset) * config.pixelsPerUnit,
      width: Math.max(duration * config.pixelsPerUnit, minWidth)
    }
  } catch (error) {
    console.error('Error calculating task position:', error)
    return { left: 0, width: 100 }
  }
}

// Calculate timeline bounds based on tasks
export const calculateTimelineBounds = (
  tasks: GanttTask[],
  timeScale: TimeScale,
  padding: number = 2
): { start: Date; end: Date } => {
  if (tasks.length === 0) {
    const today = new Date()
    const config = timeScaleConfigs[timeScale]
    return {
      start: config.addFunction(today, -padding),
      end: config.addFunction(today, padding * 2)
    }
  }
  
  const allDates = tasks.flatMap(task => [task.startDate, task.endDate])
  const minDate = new Date(Math.min(...allDates.map(d => d.getTime())))
  const maxDate = new Date(Math.max(...allDates.map(d => d.getTime())))
  
  const config = timeScaleConfigs[timeScale]
  
  return {
    start: config.addFunction(config.startFunction(minDate), -padding),
    end: config.addFunction(config.endFunction(maxDate), padding)
  }
}

// Get status color classes
export const getStatusColor = (status: GanttTask['status']): string => {
  const colors = {
    'not-started': 'bg-gray-400',
    'in-progress': 'bg-blue-500',
    'completed': 'bg-green-500',
    'on-hold': 'bg-yellow-500'
  }
  return colors[status]
}

// Get priority color classes
export const getPriorityColor = (priority: GanttTask['priority']): string => {
  const colors = {
    low: 'bg-gray-400',
    normal: 'bg-blue-500',
    high: 'bg-orange-500',
    urgent: 'bg-red-500'
  }
  return colors[priority || 'normal']
}

// Format duration for display
export const formatDuration = (startDate: Date, endDate: Date): string => {
  const days = differenceInDays(endDate, startDate) + 1
  
  if (days === 1) return '1 day'
  if (days < 7) return `${days} days`
  if (days < 30) {
    const weeks = Math.floor(days / 7)
    const remainingDays = days % 7
    if (remainingDays === 0) return `${weeks} week${weeks > 1 ? 's' : ''}`
    return `${weeks} week${weeks > 1 ? 's' : ''}, ${remainingDays} day${remainingDays > 1 ? 's' : ''}`
  }
  
  const months = Math.floor(days / 30)
  const remainingDays = days % 30
  if (remainingDays === 0) return `${months} month${months > 1 ? 's' : ''}`
  return `${months} month${months > 1 ? 's' : ''}, ${remainingDays} day${remainingDays > 1 ? 's' : ''}`
}

// Check if task is overdue
export const isTaskOverdue = (task: GanttTask): boolean => {
  const today = new Date()
  return task.endDate < today && task.status !== 'completed'
}

// Check if task is critical (high priority and in progress)
export const isTaskCritical = (task: GanttTask): boolean => {
  return (task.priority === 'high' || task.priority === 'urgent') && 
         task.status === 'in-progress'
}

// Get task completion percentage as string
export const getProgressText = (progress: number): string => {
  return `${Math.round(progress)}%`
}

// Sort tasks for display (projects first, then tasks by start date)
export const sortTasksForDisplay = (tasks: GanttTask[]): GanttTask[] => {
  return [...tasks].sort((a, b) => {
    // Projects first
    if (a.type === 'project' && b.type === 'task') return -1
    if (a.type === 'task' && b.type === 'project') return 1
    
    // Within same type, sort by start date
    return a.startDate.getTime() - b.startDate.getTime()
  })
}

// Find task dependencies
export const getTaskDependencies = (task: GanttTask, allTasks: GanttTask[]): GanttTask[] => {
  if (!task.dependencies) return []
  
  return task.dependencies
    .map(depId => allTasks.find(t => t.id === depId))
    .filter(Boolean) as GanttTask[]
}

// Check if task can start (all dependencies completed)
export const canTaskStart = (task: GanttTask, allTasks: GanttTask[]): boolean => {
  const dependencies = getTaskDependencies(task, allTasks)
  return dependencies.every(dep => dep.status === 'completed')
}

// Export data to CSV format
export const exportToCSV = (tasks: GanttTask[], timeScale: TimeScale): string => {
  const headers = [
    'Name', 'Type', 'Start Date', 'End Date', 'Duration', 'Progress', 
    'Status', 'Priority', 'Responsible', 'Textile Process', 'Odoo ID'
  ]
  
  const rows = tasks.map(task => [
    task.name,
    task.type,
    format(task.startDate, 'yyyy-MM-dd'),
    format(task.endDate, 'yyyy-MM-dd'),
    formatDuration(task.startDate, task.endDate),
    `${task.progress}%`,
    task.status,
    task.priority || 'normal',
    task.responsible,
    task.textileProcess || '',
    task.odooId?.toString() || ''
  ])
  
  return [headers, ...rows].map(row => row.join(',')).join('\n')
}
