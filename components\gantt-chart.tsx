"use client"

import React, { useState, useEffect, useRef } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Calendar, Download, FileText, Play, Pause, MoreHorizontal } from "lucide-react"

interface GanttTask {
  id: string
  name: string
  startDate: Date
  endDate: Date
  progress: number
  status: 'not-started' | 'in-progress' | 'completed' | 'on-hold'
  responsible: string
  textileProcess?: string
  parent?: string
  type: 'project' | 'task'
}

interface GanttChartProps {
  tasks?: GanttTask[]
  className?: string
}

const sampleTasks: GanttTask[] = [
  {
    id: 'proj-1',
    name: 'Cotton Bio Project',
    startDate: new Date('2024-01-15'),
    endDate: new Date('2024-04-15'),
    progress: 75,
    status: 'in-progress',
    responsible: '<PERSON>',
    type: 'project'
  },
  {
    id: 'task-1-1',
    name: 'Raw Material Sourcing',
    startDate: new Date('2024-01-15'),
    endDate: new Date('2024-02-01'),
    progress: 100,
    status: 'completed',
    responsible: 'Mike Chen',
    textileProcess: 'Sourcing',
    parent: 'proj-1',
    type: 'task'
  },
  {
    id: 'task-1-2',
    name: 'Spinning Process',
    startDate: new Date('2024-02-01'),
    endDate: new Date('2024-02-28'),
    progress: 80,
    status: 'in-progress',
    responsible: 'Anna Rodriguez',
    textileProcess: 'Spinning',
    parent: 'proj-1',
    type: 'task'
  },
  {
    id: 'task-1-3',
    name: 'Weaving & Finishing',
    startDate: new Date('2024-03-01'),
    endDate: new Date('2024-04-15'),
    progress: 30,
    status: 'in-progress',
    responsible: 'David Kim',
    textileProcess: 'Weaving',
    parent: 'proj-1',
    type: 'task'
  },
  {
    id: 'proj-2',
    name: 'Silk Premium Line',
    startDate: new Date('2024-02-01'),
    endDate: new Date('2024-06-01'),
    progress: 25,
    status: 'in-progress',
    responsible: 'Emma Wilson',
    type: 'project'
  },
  {
    id: 'task-2-1',
    name: 'Design & Planning',
    startDate: new Date('2024-02-01'),
    endDate: new Date('2024-03-01'),
    progress: 90,
    status: 'in-progress',
    responsible: 'Emma Wilson',
    textileProcess: 'Design',
    parent: 'proj-2',
    type: 'task'
  }
]

const statusColors = {
  'not-started': 'bg-gray-400',
  'in-progress': 'bg-blue-500',
  'completed': 'bg-green-500',
  'on-hold': 'bg-yellow-500'
}

const statusLabels = {
  'not-started': 'Not Started',
  'in-progress': 'In Progress',
  'completed': 'Completed',
  'on-hold': 'On Hold'
}

export function GanttChart({ tasks = sampleTasks, className }: GanttChartProps) {
  const [selectedTask, setSelectedTask] = useState<string | null>(null)
  const [timelineStart, setTimelineStart] = useState<Date>(new Date('2024-01-01'))
  const [timelineEnd, setTimelineEnd] = useState<Date>(new Date('2024-12-31'))
  const ganttRef = useRef<HTMLDivElement>(null)

  // Calculate timeline dimensions
  const timelineDays = Math.ceil((timelineEnd.getTime() - timelineStart.getTime()) / (1000 * 60 * 60 * 24))
  const dayWidth = 2 // pixels per day
  const timelineWidth = timelineDays * dayWidth

  const calculateTaskPosition = (task: GanttTask) => {
    const startOffset = Math.ceil((task.startDate.getTime() - timelineStart.getTime()) / (1000 * 60 * 60 * 24))
    const duration = Math.ceil((task.endDate.getTime() - task.startDate.getTime()) / (1000 * 60 * 60 * 24))
    
    return {
      left: startOffset * dayWidth,
      width: Math.max(duration * dayWidth, 20) // minimum 20px width
    }
  }

  const generateMonthHeaders = () => {
    const months = []
    const current = new Date(timelineStart.getFullYear(), timelineStart.getMonth(), 1)
    
    while (current <= timelineEnd) {
      const monthStart = new Date(current)
      const monthEnd = new Date(current.getFullYear(), current.getMonth() + 1, 0)
      const startOffset = Math.max(0, Math.ceil((monthStart.getTime() - timelineStart.getTime()) / (1000 * 60 * 60 * 24)))
      const endOffset = Math.min(timelineDays, Math.ceil((monthEnd.getTime() - timelineStart.getTime()) / (1000 * 60 * 60 * 24)))
      const width = (endOffset - startOffset) * dayWidth
      
      months.push({
        name: current.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        left: startOffset * dayWidth,
        width: width
      })
      
      current.setMonth(current.getMonth() + 1)
    }
    
    return months
  }

  const exportToPDF = () => {
    // Placeholder for PDF export functionality
    console.log('Exporting to PDF...')
    alert('PDF export functionality would be implemented here')
  }

  const exportToCSV = () => {
    // Placeholder for CSV export functionality
    console.log('Exporting to CSV...')
    const csvContent = tasks.map(task => 
      `${task.name},${task.startDate.toISOString()},${task.endDate.toISOString()},${task.progress},${task.status},${task.responsible}`
    ).join('\n')
    
    const blob = new Blob([`Name,Start Date,End Date,Progress,Status,Responsible\n${csvContent}`], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'gantt-chart-data.csv'
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Interactive Gantt Chart</CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              Textile project timeline and task management
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={exportToPDF}>
              <Download className="h-4 w-4 mr-2" />
              Export PDF
            </Button>
            <Button variant="outline" size="sm" onClick={exportToCSV}>
              <FileText className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="gantt-container" ref={ganttRef}>
          {/* Timeline Header */}
          <div className="flex border-b">
            <div className="w-80 p-4 border-r bg-gray-50 font-medium">
              Task / Project
            </div>
            <div className="flex-1 relative" style={{ minWidth: timelineWidth }}>
              <div className="flex">
                {generateMonthHeaders().map((month, index) => (
                  <div
                    key={index}
                    className="border-r border-gray-200 p-2 text-xs font-medium text-center bg-gray-50"
                    style={{ width: month.width, minWidth: month.width }}
                  >
                    {month.name}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Task Rows */}
          <div className="max-h-96 overflow-y-auto">
            {tasks.map((task) => {
              const position = calculateTaskPosition(task)
              const isProject = task.type === 'project'
              const isSelected = selectedTask === task.id
              
              return (
                <div
                  key={task.id}
                  className={`flex border-b hover:bg-gray-50 ${isSelected ? 'bg-blue-50' : ''}`}
                >
                  {/* Task Info */}
                  <div className="w-80 p-4 border-r">
                    <div className={`${isProject ? 'font-semibold' : 'ml-4'}`}>
                      {!isProject && '└─ '}
                      {task.name}
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge 
                        variant="secondary" 
                        className={`text-xs ${statusColors[task.status]} text-white`}
                      >
                        {statusLabels[task.status]}
                      </Badge>
                      <span className="text-xs text-gray-500">{task.responsible}</span>
                    </div>
                    {task.textileProcess && (
                      <div className="text-xs text-blue-600 mt-1">
                        Process: {task.textileProcess}
                      </div>
                    )}
                  </div>

                  {/* Timeline */}
                  <div className="flex-1 relative p-2" style={{ minWidth: timelineWidth }}>
                    <div
                      className={`absolute top-2 h-6 rounded cursor-pointer transition-all hover:opacity-80 ${
                        isProject ? 'bg-blue-600' : 'bg-green-500'
                      } ${isSelected ? 'ring-2 ring-blue-400' : ''}`}
                      style={{
                        left: position.left,
                        width: position.width
                      }}
                      onClick={() => setSelectedTask(isSelected ? null : task.id)}
                    >
                      {/* Progress Bar */}
                      <div
                        className="h-full bg-white bg-opacity-30 rounded"
                        style={{ width: `${task.progress}%` }}
                      />
                      
                      {/* Progress Label */}
                      {position.width > 40 && (
                        <div className="absolute inset-0 flex items-center justify-center text-xs text-white font-medium">
                          {task.progress}%
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Task Details Panel */}
        {selectedTask && (
          <div className="mt-4 p-4 bg-blue-50 rounded-lg border">
            {(() => {
              const task = tasks.find(t => t.id === selectedTask)
              if (!task) return null
              
              return (
                <div>
                  <h4 className="font-semibold mb-2">{task.name}</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Start Date:</span> {task.startDate.toLocaleDateString()}
                    </div>
                    <div>
                      <span className="font-medium">End Date:</span> {task.endDate.toLocaleDateString()}
                    </div>
                    <div>
                      <span className="font-medium">Progress:</span> {task.progress}%
                    </div>
                    <div>
                      <span className="font-medium">Responsible:</span> {task.responsible}
                    </div>
                    {task.textileProcess && (
                      <div className="col-span-2">
                        <span className="font-medium">Textile Process:</span> {task.textileProcess}
                      </div>
                    )}
                  </div>
                </div>
              )
            })()}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
