"use client"

import React, { useState, useEffect, useRef, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import {
  Calendar,
  Download,
  FileText,
  Play,
  Pause,
  MoreHorizontal,
  ZoomIn,
  ZoomOut,
  ChevronLeft,
  ChevronRight,
  Clock,
  CalendarDays,
  AlertTriangle,
  Target,
  Link
} from "lucide-react"
import {
  TimeScale,
  GanttTask,
  timeScaleConfigs,
  generateTimeUnits,
  calculateTaskPosition,
  calculateTimelineBounds,
  getStatusColor,
  getPriorityColor,
  formatDuration,
  isTaskOverdue,
  isTaskCritical,
  sortTasksForDisplay,
  exportToCSV
} from "@/lib/gantt-utils"

// Interface definitions (main types imported from utils)

interface GanttChartProps {
  tasks?: GanttTask[]
  className?: string
  // Odoo integration props
  onTaskUpdate?: (task: GanttTask) => void
  onTaskCreate?: (task: Partial<GanttTask>) => void
  onTaskDelete?: (taskId: string) => void
  readOnly?: boolean
  // Time scale props
  defaultTimeScale?: TimeScale
  showTimeScaleControls?: boolean
}

// Sample data with enhanced textile project information

// Status label mappings
const statusLabels = {
  'not-started': 'Not Started',
  'in-progress': 'In Progress',
  'completed': 'Completed',
  'on-hold': 'On Hold'
}

const sampleTasks: GanttTask[] = [
  {
    id: 'proj-1',
    name: 'Cotton Bio Project',
    startDate: new Date('2024-10-15'),
    endDate: new Date('2025-02-15'),
    progress: 75,
    status: 'in-progress',
    responsible: 'Sarah Johnson',
    type: 'project',
    odooId: 1001,
    priority: 'high'
  },
  {
    id: 'task-1-1',
    name: 'Raw Material Sourcing',
    startDate: new Date('2024-10-15'),
    endDate: new Date('2024-11-01'),
    progress: 100,
    status: 'completed',
    responsible: 'Mike Chen',
    textileProcess: 'Sourcing',
    parent: 'proj-1',
    type: 'task',
    odooId: 1002,
    priority: 'high'
  },
  {
    id: 'task-1-2',
    name: 'Spinning Process',
    startDate: new Date('2024-11-01'),
    endDate: new Date('2024-12-15'),
    progress: 80,
    status: 'in-progress',
    responsible: 'Anna Rodriguez',
    textileProcess: 'Spinning',
    parent: 'proj-1',
    type: 'task',
    odooId: 1003,
    priority: 'normal',
    dependencies: ['task-1-1']
  },
  {
    id: 'task-1-3',
    name: 'Weaving & Finishing',
    startDate: new Date('2024-12-15'),
    endDate: new Date('2025-02-15'),
    progress: 30,
    status: 'in-progress',
    responsible: 'David Kim',
    textileProcess: 'Weaving',
    parent: 'proj-1',
    type: 'task',
    odooId: 1004,
    priority: 'normal',
    dependencies: ['task-1-2']
  },
  {
    id: 'proj-2',
    name: 'Silk Premium Line',
    startDate: new Date('2024-11-01'),
    endDate: new Date('2025-04-01'),
    progress: 25,
    status: 'in-progress',
    responsible: 'Emma Wilson',
    type: 'project',
    odooId: 1005,
    priority: 'normal'
  },
  {
    id: 'task-2-1',
    name: 'Design & Planning',
    startDate: new Date('2024-11-01'),
    endDate: new Date('2024-12-01'),
    progress: 90,
    status: 'in-progress',
    responsible: 'Emma Wilson',
    textileProcess: 'Design',
    parent: 'proj-2',
    type: 'task',
    odooId: 1006,
    priority: 'normal'
  },
  {
    id: 'task-2-2',
    name: 'Silk Fiber Processing',
    startDate: new Date('2024-12-01'),
    endDate: new Date('2025-01-15'),
    progress: 10,
    status: 'not-started',
    responsible: 'James Liu',
    textileProcess: 'Processing',
    parent: 'proj-2',
    type: 'task',
    odooId: 1007,
    priority: 'normal',
    dependencies: ['task-2-1']
  },
  {
    id: 'proj-3',
    name: 'Wool Winter Collection',
    startDate: new Date('2024-09-01'),
    endDate: new Date('2024-12-31'),
    progress: 95,
    status: 'completed',
    responsible: 'Lisa Zhang',
    type: 'project',
    odooId: 1008,
    priority: 'low'
  }
]

const statusColors = {
  'not-started': 'bg-gray-400',
  'in-progress': 'bg-blue-500',
  'completed': 'bg-green-500',
  'on-hold': 'bg-yellow-500'
}

const statusLabels = {
  'not-started': 'Not Started',
  'in-progress': 'In Progress',
  'completed': 'Completed',
  'on-hold': 'On Hold'
}

export function GanttChart({
  tasks = sampleTasks,
  className,
  onTaskUpdate,
  onTaskCreate,
  onTaskDelete,
  readOnly = false,
  defaultTimeScale = 'month',
  showTimeScaleControls = true
}: GanttChartProps) {
  const [selectedTask, setSelectedTask] = useState<string | null>(null)
  const [currentTimeScale, setCurrentTimeScale] = useState<TimeScale>(defaultTimeScale)
  const [currentDate, setCurrentDate] = useState<Date>(new Date())
  const ganttRef = useRef<HTMLDivElement>(null)

  // Calculate timeline bounds using utility function
  const timelineBounds = useMemo(() => {
    return calculateTimelineBounds(tasks, currentTimeScale, 2)
  }, [tasks, currentTimeScale])

  // Generate timeline units using utility function
  const timelineMetrics = useMemo(() => {
    const { start, end } = timelineBounds
    const timeUnits = generateTimeUnits(start, end, currentTimeScale)
    const config = timeScaleConfigs[currentTimeScale]
    const totalWidth = timeUnits.reduce((sum, unit) => sum + unit.width, 0)

    return { timeUnits, totalWidth, config }
  }, [currentTimeScale, timelineBounds])

  // Get sorted tasks for display
  const sortedTasks = useMemo(() => {
    return sortTasksForDisplay(tasks)
  }, [tasks])

  // Navigation functions for time scale
  const navigateTimeline = (direction: 'prev' | 'next') => {
    const config = timeScaleConfigs[currentTimeScale]
    const offset = config.unitDuration * (direction === 'next' ? 1 : -1)

    setCurrentDate(prev => {
      const newDate = new Date(prev)
      newDate.setDate(newDate.getDate() + offset)
      return newDate
    })
  }

  // Zoom functions
  const zoomIn = () => {
    const scales: TimeScale[] = ['year', 'month', 'week', 'day']
    const currentIndex = scales.indexOf(currentTimeScale)
    if (currentIndex < scales.length - 1) {
      setCurrentTimeScale(scales[currentIndex + 1])
    }
  }

  const zoomOut = () => {
    const scales: TimeScale[] = ['day', 'week', 'month', 'year']
    const currentIndex = scales.indexOf(currentTimeScale)
    if (currentIndex < scales.length - 1) {
      setCurrentTimeScale(scales[currentIndex + 1])
    }
  }

  // Odoo integration helpers
  const handleTaskClick = (task: GanttTask) => {
    if (readOnly) {
      setSelectedTask(selectedTask === task.id ? null : task.id)
      return
    }

    setSelectedTask(selectedTask === task.id ? null : task.id)

    // Trigger Odoo callback if provided
    if (onTaskUpdate && task.odooId) {
      console.log('Odoo task interaction:', task.odooId)
    }
  }

  const handleTaskUpdate = (taskId: string, updates: Partial<GanttTask>) => {
    if (readOnly || !onTaskUpdate) return

    const task = tasks.find(t => t.id === taskId)
    if (task) {
      const updatedTask = { ...task, ...updates }
      onTaskUpdate(updatedTask)
    }
  }

  // Export functions with enhanced data
  const exportToPDF = () => {
    console.log('Exporting to PDF with time scale:', currentTimeScale)
    // In real implementation, this would generate a PDF with the current view
    alert(`PDF export for ${timeScaleConfigs[currentTimeScale].label} would be implemented here`)
  }

  const handleExportToCSV = () => {
    console.log('Exporting to CSV...')
    const csvContent = exportToCSV(tasks, currentTimeScale)

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `gantt-chart-${currentTimeScale}-view.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col gap-4">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="flex items-center gap-2">
                <CalendarDays className="h-5 w-5" />
                Interactive Gantt Chart
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Textile project timeline and task management • {timeScaleConfigs[currentTimeScale].label}
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={exportToPDF}>
                <Download className="h-4 w-4 mr-2" />
                Export PDF
              </Button>
              <Button variant="outline" size="sm" onClick={handleExportToCSV}>
                <FileText className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
            </div>
          </div>

          {/* Time Scale Controls */}
          {showTimeScaleControls && (
            <div className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">Time Scale:</span>
                  <Select value={currentTimeScale} onValueChange={(value: TimeScale) => setCurrentTimeScale(value)}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="day">Day View</SelectItem>
                      <SelectItem value="week">Week View</SelectItem>
                      <SelectItem value="month">Month View</SelectItem>
                      <SelectItem value="year">Year View</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator orientation="vertical" className="h-6" />

                <div className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={zoomOut}
                    disabled={currentTimeScale === 'year'}
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={zoomIn}
                    disabled={currentTimeScale === 'day'}
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigateTimeline('prev')}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm font-medium min-w-24 text-center">
                  {currentDate.toLocaleDateString('en-US', {
                    month: 'short',
                    year: 'numeric'
                  })}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigateTimeline('next')}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="gantt-container" ref={ganttRef}>
          {/* Timeline Header */}
          <div className="flex border-b">
            <div className="w-80 p-4 border-r bg-gray-50 font-medium">
              <div className="flex items-center justify-between">
                <span>Task / Project</span>
                <Badge variant="secondary" className="text-xs">
                  {tasks.length} items
                </Badge>
              </div>
            </div>
            <div className="flex-1 relative" style={{ minWidth: timelineMetrics.totalWidth }}>
              {/* Main timeline headers */}
              <div className="flex border-b">
                {timelineMetrics.timeUnits.map((unit, index) => (
                  <div
                    key={index}
                    className="border-r border-gray-200 p-2 text-xs font-medium text-center bg-gray-50 flex-shrink-0"
                    style={{ width: timelineMetrics.config.pixelsPerUnit }}
                  >
                    <div className="truncate">{unit.label}</div>
                    {unit.subLabel && (
                      <div className="text-gray-500 text-xs mt-1 truncate">
                        {unit.subLabel}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Task Rows */}
          <div className="max-h-96 overflow-y-auto">
            {sortedTasks.map((task) => {
              const position = calculateTaskPosition(task, timelineBounds.start, currentTimeScale)
              const isProject = task.type === 'project'
              const isSelected = selectedTask === task.id
              const taskOverdue = isTaskOverdue(task)
              const taskCritical = isTaskCritical(task)

              return (
                <div
                  key={task.id}
                  className={`flex border-b hover:bg-gray-50 transition-colors ${
                    isSelected ? 'bg-blue-50 border-blue-200' : ''
                  } ${taskOverdue ? 'bg-red-50' : ''}`}
                >
                  {/* Task Info */}
                  <div className="w-80 p-4 border-r">
                    <div className={`flex items-center gap-2 ${isProject ? 'font-semibold' : 'ml-4'}`}>
                      {!isProject && <span className="text-gray-400">└─</span>}
                      <span className="flex-1 truncate">{task.name}</span>

                      {/* Status indicators */}
                      <div className="flex items-center gap-1">
                        {taskOverdue && (
                          <AlertTriangle className="w-3 h-3 text-red-500" title="Overdue" />
                        )}
                        {taskCritical && (
                          <Target className="w-3 h-3 text-orange-500" title="Critical Task" />
                        )}
                        {task.dependencies && task.dependencies.length > 0 && (
                          <Link className="w-3 h-3 text-blue-500" title="Has Dependencies" />
                        )}
                        {task.priority && (
                          <div
                            className={`w-2 h-2 rounded-full ${getPriorityColor(task.priority)}`}
                            title={`Priority: ${task.priority}`}
                          />
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2 mt-2">
                      <Badge
                        variant="secondary"
                        className={`text-xs ${getStatusColor(task.status)} text-white`}
                      >
                        {statusLabels[task.status]}
                      </Badge>
                      <span className="text-xs text-gray-500 truncate">{task.responsible}</span>
                    </div>

                    {task.textileProcess && (
                      <div className="text-xs text-blue-600 mt-1">
                        Process: {task.textileProcess}
                      </div>
                    )}

                    <div className="flex items-center justify-between text-xs text-gray-500 mt-1">
                      <span>{formatDuration(task.startDate, task.endDate)}</span>
                      {task.odooId && (
                        <span className="text-gray-400">ID: {task.odooId}</span>
                      )}
                    </div>

                    <div className="text-xs text-gray-500 mt-1">
                      {task.startDate.toLocaleDateString()} - {task.endDate.toLocaleDateString()}
                    </div>
                  </div>

                  {/* Timeline */}
                  <div className="flex-1 relative p-2" style={{ minWidth: timelineMetrics.totalWidth }}>
                    <div
                      className={`absolute top-2 h-6 rounded cursor-pointer transition-all hover:shadow-md hover:scale-105 ${
                        taskOverdue ? 'bg-red-600' :
                        taskCritical ? 'bg-orange-600' :
                        isProject ? 'bg-blue-600' : 'bg-green-500'
                      } ${isSelected ? 'ring-2 ring-blue-400 ring-offset-1' : ''} ${
                        task.status === 'completed' ? 'opacity-75' : ''
                      }`}
                      style={{
                        left: position.left,
                        width: position.width
                      }}
                      onClick={() => handleTaskClick(task)}
                      title={`${task.name} (${task.progress}%) - ${formatDuration(task.startDate, task.endDate)}`}
                    >
                      {/* Progress Bar */}
                      <div
                        className={`h-full rounded transition-all ${
                          task.status === 'completed' ? 'bg-white bg-opacity-50' : 'bg-white bg-opacity-30'
                        }`}
                        style={{ width: `${task.progress}%` }}
                      />

                      {/* Progress Label */}
                      {position.width > 50 && (
                        <div className="absolute inset-0 flex items-center justify-center text-xs text-white font-medium">
                          {task.progress}%
                        </div>
                      )}

                      {/* Task status indicators */}
                      <div className="absolute -top-1 -right-1 flex gap-1">
                        {task.status === 'completed' && (
                          <div className="w-2 h-2 bg-green-400 rounded-full" title="Completed" />
                        )}
                        {taskOverdue && (
                          <div className="w-2 h-2 bg-red-400 rounded-full" title="Overdue" />
                        )}
                      </div>

                      {/* Task dependencies indicators */}
                      {task.dependencies && task.dependencies.length > 0 && (
                        <div className="absolute -left-2 top-1 w-1 h-4 bg-yellow-400 rounded-full"
                             title={`Depends on: ${task.dependencies.join(', ')}`} />
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Enhanced Task Details Panel */}
        {selectedTask && (
          <div className="mt-4 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
            {(() => {
              const task = tasks.find(t => t.id === selectedTask)
              if (!task) return null

              const durationDays = Math.ceil((task.endDate.getTime() - task.startDate.getTime()) / (1000 * 60 * 60 * 24))

              return (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold text-gray-900">{task.name}</h4>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant="secondary"
                        className={`${statusColors[task.status]} text-white`}
                      >
                        {statusLabels[task.status]}
                      </Badge>
                      {task.priority && (
                        <Badge variant="outline" className="capitalize">
                          {task.priority} Priority
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Timeline Information */}
                    <div className="space-y-3">
                      <h5 className="font-medium text-gray-700 flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Timeline
                      </h5>
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="font-medium">Start:</span> {task.startDate.toLocaleDateString('en-US', {
                            weekday: 'short',
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })}
                        </div>
                        <div>
                          <span className="font-medium">End:</span> {task.endDate.toLocaleDateString('en-US', {
                            weekday: 'short',
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })}
                        </div>
                        <div>
                          <span className="font-medium">Duration:</span> {formatDuration(task.startDate, task.endDate)}
                        </div>
                        {isTaskOverdue(task) && (
                          <div className="text-red-600 font-medium flex items-center gap-1">
                            <AlertTriangle className="h-3 w-3" />
                            Overdue
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Progress Information */}
                    <div className="space-y-3">
                      <h5 className="font-medium text-gray-700">Progress & Status</h5>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>Completion</span>
                          <span className="font-medium">{task.progress}%</span>
                        </div>
                        <Progress value={task.progress} className="h-2" />
                        <div className="text-sm">
                          <span className="font-medium">Responsible:</span> {task.responsible}
                        </div>
                      </div>
                    </div>

                    {/* Additional Information */}
                    <div className="space-y-3">
                      <h5 className="font-medium text-gray-700">Details</h5>
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="font-medium">Type:</span> {task.type === 'project' ? 'Project' : 'Task'}
                        </div>
                        {task.textileProcess && (
                          <div>
                            <span className="font-medium">Process:</span> {task.textileProcess}
                          </div>
                        )}
                        {task.odooId && (
                          <div>
                            <span className="font-medium">Odoo ID:</span> {task.odooId}
                          </div>
                        )}
                        {task.dependencies && task.dependencies.length > 0 && (
                          <div>
                            <span className="font-medium">Dependencies:</span>
                            <div className="mt-1">
                              {task.dependencies.map(dep => (
                                <Badge key={dep} variant="outline" className="text-xs mr-1">
                                  {dep}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  {!readOnly && (
                    <div className="flex gap-2 mt-6 pt-4 border-t border-blue-200">
                      <Button size="sm" variant="outline">
                        Edit Task
                      </Button>
                      <Button size="sm" variant="outline">
                        View in Odoo
                      </Button>
                      <Button size="sm" variant="outline">
                        Add Dependency
                      </Button>
                    </div>
                  )}
                </div>
              )
            })()}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
