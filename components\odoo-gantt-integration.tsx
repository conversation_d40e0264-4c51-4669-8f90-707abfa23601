"use client"

import React from 'react'
import { GanttChart } from './gantt-chart'

// Odoo 18 Integration Types
interface OdooProjectTask {
  id: number
  name: string
  project_id: [number, string]
  user_ids: Array<[number, string]>
  date_start: string
  date_end: string
  progress: number
  stage_id: [number, string]
  priority: '0' | '1' | '2' | '3' // Odoo priority levels
  parent_id?: [number, string]
  textile_process?: string
  production_capacity?: number
  quality_grade?: string
  textile_type?: string
  depends_on_ids?: number[]
}

interface OdooGanttProps {
  // Odoo RPC service
  rpc?: any
  // Odoo context
  context?: Record<string, any>
  // Domain filter for tasks
  domain?: any[]
  // Model name (usually 'project.task')
  model?: string
  // View configuration
  viewConfig?: {
    date_start: string
    date_stop: string
    progress: string
    consolidation: string
    consolidation_max: Record<string, any>
    string: string
  }
  // Callbacks
  onTaskWrite?: (taskId: number, values: Record<string, any>) => Promise<void>
  onTaskCreate?: (values: Record<string, any>) => Promise<number>
  onTaskUnlink?: (taskIds: number[]) => Promise<void>
  // UI options
  readOnly?: boolean
  showTimeScaleControls?: boolean
}

// Convert Odoo task to internal format
const convertOdooTaskToGantt = (odooTask: OdooProjectTask) => {
  const statusMap = {
    'draft': 'not-started' as const,
    'in_progress': 'in-progress' as const,
    'done': 'completed' as const,
    'cancelled': 'on-hold' as const
  }

  const priorityMap = {
    '0': 'low' as const,
    '1': 'normal' as const,
    '2': 'high' as const,
    '3': 'urgent' as const
  }

  return {
    id: odooTask.id.toString(),
    name: odooTask.name,
    startDate: new Date(odooTask.date_start),
    endDate: new Date(odooTask.date_end),
    progress: odooTask.progress || 0,
    status: statusMap[odooTask.stage_id[1] as keyof typeof statusMap] || 'not-started',
    responsible: odooTask.user_ids.length > 0 ? odooTask.user_ids[0][1] : 'Unassigned',
    textileProcess: odooTask.textile_process,
    parent: odooTask.parent_id ? odooTask.parent_id[0].toString() : undefined,
    type: odooTask.parent_id ? 'task' as const : 'project' as const,
    odooId: odooTask.id,
    projectId: odooTask.project_id[0],
    priority: priorityMap[odooTask.priority] || 'normal',
    dependencies: odooTask.depends_on_ids?.map(id => id.toString()) || []
  }
}

// Convert internal format back to Odoo
const convertGanttTaskToOdoo = (ganttTask: any): Partial<OdooProjectTask> => {
  const priorityMap = {
    'low': '0',
    'normal': '1',
    'high': '2',
    'urgent': '3'
  }

  return {
    name: ganttTask.name,
    date_start: ganttTask.startDate.toISOString().split('T')[0],
    date_end: ganttTask.endDate.toISOString().split('T')[0],
    progress: ganttTask.progress,
    priority: priorityMap[ganttTask.priority as keyof typeof priorityMap] || '1',
    textile_process: ganttTask.textileProcess,
    depends_on_ids: ganttTask.dependencies?.map((dep: string) => parseInt(dep)) || []
  }
}

// Main Odoo Gantt Integration Component
export function OdooGanttIntegration({
  rpc,
  context = {},
  domain = [],
  model = 'project.task',
  viewConfig,
  onTaskWrite,
  onTaskCreate,
  onTaskUnlink,
  readOnly = false,
  showTimeScaleControls = true
}: OdooGanttProps) {
  const [tasks, setTasks] = React.useState<any[]>([])
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)

  // Load tasks from Odoo
  const loadTasks = React.useCallback(async () => {
    if (!rpc) {
      // Fallback to sample data if no RPC service
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const fields = [
        'name', 'project_id', 'user_ids', 'date_start', 'date_end',
        'progress', 'stage_id', 'priority', 'parent_id',
        'textile_process', 'production_capacity', 'quality_grade',
        'textile_type', 'depends_on_ids'
      ]

      const odooTasks = await rpc('/web/dataset/search_read', {
        model,
        domain,
        fields,
        context
      })

      const convertedTasks = odooTasks.records.map(convertOdooTaskToGantt)
      setTasks(convertedTasks)
    } catch (err) {
      console.error('Error loading Odoo tasks:', err)
      setError('Failed to load tasks from Odoo')
    } finally {
      setLoading(false)
    }
  }, [rpc, model, domain, context])

  // Load tasks on mount
  React.useEffect(() => {
    loadTasks()
  }, [loadTasks])

  // Handle task updates
  const handleTaskUpdate = async (task: any) => {
    if (!onTaskWrite || !task.odooId) return

    try {
      const odooValues = convertGanttTaskToOdoo(task)
      await onTaskWrite(task.odooId, odooValues)
      
      // Reload tasks to get updated data
      await loadTasks()
    } catch (err) {
      console.error('Error updating task in Odoo:', err)
      setError('Failed to update task')
    }
  }

  // Handle task creation
  const handleTaskCreate = async (taskData: any) => {
    if (!onTaskCreate) return

    try {
      const odooValues = convertGanttTaskToOdoo(taskData)
      const newTaskId = await onTaskCreate(odooValues)
      
      // Reload tasks to include the new task
      await loadTasks()
      return newTaskId
    } catch (err) {
      console.error('Error creating task in Odoo:', err)
      setError('Failed to create task')
    }
  }

  // Handle task deletion
  const handleTaskDelete = async (taskId: string) => {
    const task = tasks.find(t => t.id === taskId)
    if (!onTaskUnlink || !task?.odooId) return

    try {
      await onTaskUnlink([task.odooId])
      
      // Reload tasks to reflect deletion
      await loadTasks()
    } catch (err) {
      console.error('Error deleting task in Odoo:', err)
      setError('Failed to delete task')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Gantt chart data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="text-red-600 font-medium">Error loading Gantt chart</div>
        </div>
        <p className="text-red-600 text-sm mt-1">{error}</p>
        <button 
          onClick={loadTasks}
          className="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <GanttChart
      tasks={tasks}
      onTaskUpdate={handleTaskUpdate}
      onTaskCreate={handleTaskCreate}
      onTaskDelete={handleTaskDelete}
      readOnly={readOnly}
      showTimeScaleControls={showTimeScaleControls}
      defaultTimeScale="month"
    />
  )
}

// Odoo OWL Component Wrapper (for backend integration)
export const createOdooOwlGanttComponent = () => {
  return `
/** @odoo-module **/

import { Component, onMounted, useRef, useState } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

export class OdooGanttChartWidget extends Component {
  setup() {
    this.rpc = useService("rpc");
    this.ganttRef = useRef("ganttContainer");
    this.state = useState({
      ganttData: null,
      loading: true,
      timeScale: 'month'
    });

    onMounted(() => {
      this.loadGanttChart();
    });
  }

  async loadGanttChart() {
    try {
      const data = await this.rpc("/web/dataset/search_read", {
        model: this.props.model || 'project.task',
        domain: this.props.domain || [],
        fields: [
          'name', 'project_id', 'user_ids', 'date_start', 'date_end',
          'progress', 'stage_id', 'priority', 'parent_id',
          'textile_process', 'production_capacity', 'quality_grade'
        ],
        context: this.props.context || {}
      });

      this.state.ganttData = data.records;
      this.state.loading = false;
      this.renderReactGantt();
    } catch (error) {
      console.error("Error loading Gantt data:", error);
      this.state.loading = false;
    }
  }

  renderReactGantt() {
    // This would integrate with the React component
    // Implementation depends on Odoo's React integration setup
    console.log("Rendering React Gantt with data:", this.state.ganttData);
  }

  async updateTask(taskId, values) {
    try {
      await this.rpc("/web/dataset/call_kw", {
        model: this.props.model || 'project.task',
        method: 'write',
        args: [[taskId], values],
        kwargs: { context: this.props.context || {} }
      });
      
      // Reload data
      await this.loadGanttChart();
    } catch (error) {
      console.error("Error updating task:", error);
    }
  }
}

OdooGanttChartWidget.template = "gantt_textile.OdooGanttChartTemplate";
OdooGanttChartWidget.props = {
  model: { type: String, optional: true },
  domain: { type: Array, optional: true },
  context: { type: Object, optional: true },
  readonly: { type: Boolean, optional: true }
};

registry.category("fields").add("odoo_gantt_chart", OdooGanttChartWidget);
`;
}
