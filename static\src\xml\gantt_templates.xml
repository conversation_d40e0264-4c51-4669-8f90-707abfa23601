<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="gantt_textile.GanttChartTemplate" owl="1">
        <div class="o_gantt_textile_container">
            <!-- Loading State -->
            <div t-if="state.loading" class="o_gantt_loading">
                <div class="text-center p-4">
                    <i class="fa fa-spinner fa-spin fa-2x text-muted mb-3"></i>
                    <p class="text-muted">Loading Gantt Chart...</p>
                </div>
            </div>

            <!-- Error State -->
            <div t-if="state.error" class="alert alert-danger">
                <strong>Error:</strong> <t t-esc="state.error"/>
                <button class="btn btn-sm btn-outline-danger ml-2" t-on-click="loadGanttChart">
                    <i class="fa fa-refresh"></i> Retry
                </button>
            </div>

            <!-- Main Gantt Content -->
            <div t-if="!state.loading and !state.error" class="o_gantt_content">
                <!-- Gantt Header Controls -->
                <div class="o_gantt_header bg-light p-3 border-bottom">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="mb-0">
                                <i class="fa fa-calendar-o mr-2"></i>
                                Textile Project Gantt Chart
                            </h4>
                            <small class="text-muted">
                                Interactive timeline for textile manufacturing projects
                            </small>
                        </div>
                        <div class="col-md-6 text-right">
                            <!-- Time Scale Controls -->
                            <div class="btn-group mr-2" role="group">
                                <button type="button"
                                        class="btn btn-sm btn-outline-primary"
                                        t-att-class="{'active': state.timeScale === 'day'}"
                                        t-on-click="() => this.setTimeScale('day')">
                                    Day
                                </button>
                                <button type="button"
                                        class="btn btn-sm btn-outline-primary"
                                        t-att-class="{'active': state.timeScale === 'week'}"
                                        t-on-click="() => this.setTimeScale('week')">
                                    Week
                                </button>
                                <button type="button"
                                        class="btn btn-sm btn-outline-primary"
                                        t-att-class="{'active': state.timeScale === 'month'}"
                                        t-on-click="() => this.setTimeScale('month')">
                                    Month
                                </button>
                                <button type="button"
                                        class="btn btn-sm btn-outline-primary"
                                        t-att-class="{'active': state.timeScale === 'year'}"
                                        t-on-click="() => this.setTimeScale('year')">
                                    Year
                                </button>
                            </div>

                            <!-- Export Controls -->
                            <div class="btn-group" role="group">
                                <button type="button"
                                        class="btn btn-sm btn-outline-secondary"
                                        t-on-click="exportToPDF">
                                    <i class="fa fa-file-pdf-o mr-1"></i> PDF
                                </button>
                                <button type="button"
                                        class="btn btn-sm btn-outline-secondary"
                                        t-on-click="exportToCSV">
                                    <i class="fa fa-file-excel-o mr-1"></i> CSV
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- React Gantt Container -->
                <div class="o_gantt_react_container" t-ref="ganttContainer">
                    <!-- React component will be mounted here -->
                </div>
            </div>
        </div>
    </t>
</templates>
